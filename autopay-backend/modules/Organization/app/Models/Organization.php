<?php

namespace Modules\Organization\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Modules\Core\Models\Concerns\HasSchemalessAttributes;
use Modules\Organization\Models\Company;
use Modules\Organization\Traits\OrganizationHasSubscriptions;
use Modules\Team\Models\Team;
use Modules\User\Models\ModelHasRole;
use Modules\User\Models\Role;
use Modules\User\Models\User;

/**
 * Organization Model
 *
 * Architecture Notes:
 * - Organizations contain Teams and Roles
 * - Teams are organizational groups (like departments) - NO DIRECT PERMISSIONS
 * - Roles contain permissions and define what users can do
 * - Users can have MULTIPLE teams with DIFFERENT roles per organization
 * - Permissions are ALWAYS role-based, never team-based
 * - Teams are just for grouping/organizing users
 */
class Organization extends Model
{
    use HasFactory, HasSchemalessAttributes, HasUlids, OrganizationHasSubscriptions;

    protected $guarded = [];

    protected $casts = [
        'data' => 'object',
        'is_active' => 'boolean',
    ];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Modules\Organization\Database\Factories\OrganizationFactory::new();
    }

    /**
     * Boot the model
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(static function ($organization) {
            if (empty($organization->alias)) {
                $organization->alias = static::generateUniqueAlias($organization->name);
            }
        });
    }

    /**
     * Generate a unique alias for the organization
     */
    public static function generateUniqueAlias(string $name): string
    {
        $baseAlias = Str::slug($name);
        $alias = $baseAlias;
        $counter = 1;

        // Check if alias already exists and append a number if needed
        while (static::whereAlias($alias)->exists()) {
            $alias = $baseAlias.'-'.$counter;
            $counter++;
        }

        return $alias;
    }

    /**
     * Organization owner
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Organization users through model_has_roles (derived from teams)
     * This method returns all users regardless of active status - use activeUsers() for active only
     */
    public function users(): Collection
    {
        $teamIds = $this->teams()->pluck('id');

        $userIds = ModelHasRole::whereIn('team_id', $teamIds)
            ->where('model_type', User::class)
            ->pluck('model_id')
            ->unique();

        return User::whereIn('id', $userIds)->get();
    }

    /**
     * Get users to count for this organization
     */
    public function getUsersCountAttribute(): int
    {
        $teamIds = $this->teams()->pluck('id');

        return ModelHasRole::whereIn('team_id', $teamIds)
            ->where('model_type', User::class)
            ->distinct('model_id')
            ->count('model_id');
    }

    /**
     * Active organization users (based on team and role active status)
     */
    public function activeUsers(): Collection
    {
        // Get active teams
        $activeTeamIds = OrganizationTeam::where('organization_id', $this->id)
            ->where('is_active', true)
            ->pluck('team_id');

        // Get users with active roles in active teams
        $userIds = ModelHasRole::whereIn('team_id', $activeTeamIds)
            ->where('model_type', User::class)
            ->where('is_active', true)  // Only active role assignments
            ->pluck('model_id')
            ->unique();

        return User::whereIn('id', $userIds)->get();
    }

    /**
     * Organization teams
     */
    public function teams(): HasMany
    {
        return $this->hasMany(Team::class, 'organization_id');
    }

    /**
     * Organization roles
     */
    public function roles(): HasMany
    {
        return $this->hasMany(Role::class, 'organization_id');
    }

    /**
     * Organization invites
     */
    public function invites(): HasMany
    {
        return $this->hasMany(OrganizationInvite::class, 'organization_id');
    }

    /**
     * Organization members
     */
    public function members(): HasMany
    {
        return $this->hasMany(\Modules\Member\Models\Member::class, 'organization_id');
    }

    /**
     * Organization domains
     */
    public function domains(): HasMany
    {
        return $this->hasMany(Domain::class, 'organization_id');
    }

    /**
     * Organization company
     */
    public function company(): HasOne
    {
        return $this->hasOne(Company::class, 'organization_id');
    }

    /**
     * Check if a user is an owner of an organization
     */
    public function isOwner(User $user): bool
    {
        return $this->owner_id === $user->id;
    }

    /**
     * Check if user is member of organization (has an active role in any active team)
     */
    public function hasMember(User $user): bool
    {
        return ModelHasRole::where('model_id', $user->id)
            ->where('model_type', User::class)
            ->where('is_active', true)
            ->whereHas('team', function ($query) {
                $query->where('organization_id', $this->id)
                    ->whereHas('organizationTeam', function ($subQuery) {
                        $subQuery->where('is_active', true);
                    });
            })
            ->exists();
    }

    /**
     * Ensure an organization-team relationship exists (creates organization_team record)
     * Note: This method only creates the organization-team relationship metadata.
     * User-team assignment is handled through role assignments in model_has_roles.
     */
    public function ensureOrganizationTeamExists(Team $team, array $additionalData = []): void
    {
        // Validate that team belongs to this organization
        if ($team->organization_id !== $this->id) {
            throw new InvalidArgumentException('Team does not belong to this organization');
        }

        // Create organization_team_user record (without user_id)
        $orgTeamData = array_merge([
            'organization_id' => $this->id,
            'team_id' => $team->id,
            'is_active' => true,
        ], $additionalData);

        // Check if the organization_team record already exists
        $existingOrgTeam = OrganizationTeam::where('organization_id', $this->id)
            ->where('team_id', $team->id)
            ->first();

        if (! $existingOrgTeam) {
            OrganizationTeam::create($orgTeamData);
        }
    }

    /**
     * Add user to organization with team and role (ensures 1 role per team)
     */
    public function addUserToTeamWithRole(User $user, Team $team, Role $role, array $additionalData = []): void
    {
        // First ensure organization_team record exists
        $this->ensureOrganizationTeamExists($team, $additionalData);

        // Then assign a role (this will enforce 1 role per team constraint)
        $this->assignUserRoleInTeam($user, $team, $role);
    }

    /**
     * Get all teams a user belongs to in this organization
     */
    public function getUserTeams(User $user): Collection
    {
        // Get team data with role_id from the model_has_roles table
        $teamRoleData = ModelHasRole::where('model_id', $user->id)
            ->where('model_type', User::class)
            ->where('is_active', true)  // Only active role assignments
            ->whereNotNull('team_id')
            ->select('team_id', 'role_id')
            ->get()
            ->keyBy('team_id');

        $teamIds = $teamRoleData->pluck('team_id');

        return $this->teams()->whereIn('id', $teamIds)->get()->map(function ($team) use ($teamRoleData) {
            $orgTeam = OrganizationTeam::where('organization_id', $this->id)
                ->where('team_id', $team->id)
                ->first();

            $roleData = $teamRoleData->get($team->id);

            return [
                'team_id' => $team->id,
                'role_id' => $roleData?->role_id,
                'is_active' => $orgTeam?->is_active ?? true,
            ];
        });
    }

    /**
     * Get a user's role in a specific team
     */
    public function getUserRoleInTeam(User $user, Team $team): ?Role
    {
        // Check if a user has a role in this team through model_has_roles
        return $user->getRolesInTeam($team)->first();
    }

    /**
     * Check if a user belongs to a specific team in this organization
     */
    public function userBelongsToTeam(User $user, Team $team): bool
    {
        // Check if a user has any active role in this team
        $hasRole = ModelHasRole::where('model_id', $user->id)
            ->where('model_type', User::class)
            ->where('team_id', $team->id)
            ->where('is_active', true)  // Only active role assignments
            ->exists();

        if (! $hasRole) {
            return false;
        }

        // Check if the organization_team record is active
        $orgTeam = OrganizationTeam::where('organization_id', $this->id)
            ->where('team_id', $team->id)
            ->first();

        return $orgTeam?->is_active ?? true;
    }

    /**
     * Remove a user from a specific team
     */
    public function removeUserFromTeam(User $user, Team $team): bool
    {
        // Remove all user's role assignments in this team (both active and inactive)
        $roleAssignments = $user->getRoleAssignmentsInTeam($team);
        foreach ($roleAssignments as $roleAssignment) {
            $roleAssignment->delete();
        }

        return true;
    }

    /**
     * Update user's role in a specific team
     */
    public function updateUserRoleInTeam(User $user, Team $team, Role $role): bool
    {
        // Validate that role belongs to this organization
        if ($role->organization_id !== $this->id) {
            throw new InvalidArgumentException('Role does not belong to this organization');
        }

        // Remove old roles in this team (ensure only 1 role per team)
        $oldRoles = $user->getRolesInTeam($team);
        foreach ($oldRoles as $oldRole) {
            $user->removeRoleInTeam($oldRole, $team);
        }

        // Assign a new role
        $user->assignRoleInTeam($role, $team);

        return true;
    }

    /**
     * Assign a single role to a user in a team (ensures 1 role per team constraint)
     */
    public function assignUserRoleInTeam(User $user, Team $team, Role $role): void
    {
        // Validate that team belongs to this organization
        if ($team->organization_id !== $this->id) {
            throw new InvalidArgumentException('Team does not belong to this organization');
        }

        // Validate that role belongs to this organization
        if ($role->organization_id !== $this->id) {
            throw new InvalidArgumentException('Role does not belong to this organization');
        }

        // Remove any existing roles in this team first (enforce 1 role per team)
        $existingRoles = $user->getRolesInTeam($team);
        foreach ($existingRoles as $existingRole) {
            $user->removeRoleInTeam($existingRole, $team);
        }

        // Assign the new role
        $user->assignRoleInTeam($role, $team);
    }

    /**
     * Disable user role in a specific team
     */
    public function disableUserRoleInTeam(User $user, Team $team): bool
    {
        return ModelHasRole::where('model_id', $user->id)
            ->where('model_type', User::class)
            ->where('team_id', $team->id)
            ->update(['is_active' => false]) > 0;
    }

    /**
     * Enable a user role in a specific team
     */
    public function enableUserRoleInTeam(User $user, Team $team): bool
    {
        return ModelHasRole::where('model_id', $user->id)
            ->where('model_type', User::class)
            ->where('team_id', $team->id)
            ->update(['is_active' => true]) > 0;
    }

    /**
     * Disable a team (affects all users in the team)
     */
    public function disableTeam(Team $team): bool
    {
        // Validate that team belongs to this organization
        if ($team->organization_id !== $this->id) {
            throw new InvalidArgumentException('Team does not belong to this organization');
        }

        return OrganizationTeam::where('organization_id', $this->id)
            ->where('team_id', $team->id)
            ->update(['is_active' => false]);
    }

    /**
     * Enable a team
     */
    public function enableTeam(Team $team): bool
    {
        // Validate that team belongs to this organization
        if ($team->organization_id !== $this->id) {
            throw new InvalidArgumentException('Team does not belong to this organization');
        }

        return OrganizationTeam::where('organization_id', $this->id)
            ->where('team_id', $team->id)
            ->update(['is_active' => true]);
    }

    /**
     * Remove user from the organization
     */
    public function removeUser(User $user): void
    {
        // Get all teams in this organization that the user belongs to
        $userTeams = $this->getUserTeams($user);

        foreach ($userTeams as $teamData) {
            $team = Team::find($teamData['team_id']);
            if ($team) {
                $this->removeUserFromTeam($user, $team);
            }
        }
    }

    /**
     * Check if a user has specific permission in this organization
     * Checks across all user's roles in all active teams
     */
    public function userHasPermission(User $user, string $permission): bool
    {
        $userTeams = $this->getUserTeams($user);

        foreach ($userTeams as $teamData) {
            // Skip inactive teams
            if (! $teamData['is_active']) {
                continue;
            }

            $team = Team::find($teamData['team_id']);
            if ($team && $user->hasPermissionInTeam($permission, $team)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get all permissions for user in this organization across all active teams
     */
    public function getUserPermissions(User $user): Collection
    {
        $userTeams = $this->getUserTeams($user);
        $permissions = collect();

        foreach ($userTeams as $teamData) {
            // Skip inactive teams
            if (! $teamData['is_active']) {
                continue;
            }

            $team = Team::find($teamData['team_id']);
            if ($team) {
                $roles = $user->getRolesInTeam($team);
                foreach ($roles as $role) {
                    $permissions = $permissions->merge($role->permissions);
                }
            }
        }

        return $permissions->unique('id');
    }

    /**
     * Assign permission to a user's role in a specific team
     */
    public function giveUserPermissionInTeam(User $user, Team $team, string $permission): void
    {
        $role = $this->getUserRoleInTeam($user, $team);
        $role?->givePermissionTo($permission);
    }

    /**
     * Remove permission from a user's role in a specific team
     */
    public function revokeUserPermissionInTeam(User $user, Team $team, string $permission): void
    {
        $role = $this->getUserRoleInTeam($user, $team);
        $role?->revokePermissionTo($permission);
    }

    /**
     * Get or create a default role for this organization
     */
    public function getOrCreateDefaultRole(): Role
    {
        $defaultRole = $this->roles()->where('is_default', true)->first();

        if (! $defaultRole) {
            $defaultRole = $this->roles()->create([
                'name' => 'Member',
                'guard_name' => Auth::getDefaultDriver(),
                'description' => 'Default organization member role',
                'is_default' => true,
            ]);
        }

        return $defaultRole;
    }
}
